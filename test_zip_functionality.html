<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZIP Code Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        input { padding: 5px; margin: 5px; }
        button { padding: 8px 15px; margin: 5px; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>ZIP Code Functionality Test</h1>
    
    <div class="test-section">
        <h3>Test ZIP Code Changes</h3>
        <p>Enter different ZIP codes to test the geocoding functionality:</p>
        
        <label for="testZip">ZIP Code:</label>
        <input type="text" id="testZip" value="60010" maxlength="5" pattern="[0-9]{5}">
        <button onclick="testZipCode()">Test ZIP</button>
        
        <div id="zipResults"></div>
    </div>
    
    <div class="test-section">
        <h3>Test Known ZIP Codes</h3>
        <button onclick="testKnownZips()">Test Multiple ZIP Codes</button>
        <div id="knownZipResults"></div>
    </div>
    
    <div class="test-section">
        <h3>Test Invalid ZIP Codes</h3>
        <button onclick="testInvalidZips()">Test Invalid ZIP Codes</button>
        <div id="invalidZipResults"></div>
    </div>
    
    <div id="results"></div>

    <script>
        // Mock the global variables and functions that would exist in the main app
        window.LAT = 42.1543;
        window.LNG = -88.1362;
        window.ZIP_CODE = "60010";
        
        // Mock DOM elements
        const mockZipDisplay = { textContent: "60010" };
        const mockCoordsDisplay = { textContent: "42.1543, -88.1362" };
        const mockStatusMessage = { textContent: "", style: { color: "" } };
        
        // Simplified version of the geocodeZipCode function for testing
        window.geocodeZipCode = async function(zipCode) {
            const zipCoordinates = {
                "60010": { lat: 42.1543, lng: -88.1362 }, // Barrington, IL
                "60004": { lat: 42.0828, lng: -87.9803 }, // Arlington Heights, IL
                "60601": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (The Loop)
                "60614": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Lincoln Park)
                "60622": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Wicker Park/Bucktown)
                "60637": { lat: 41.8781, lng: -87.6298 }, // Chicago, IL (Hyde Park)
                "10001": { lat: 40.7503, lng: -73.9972 }, // Manhattan, NY
                "90210": { lat: 34.0901, lng: -118.4065 }, // Beverly Hills, CA
                "89101": { lat: 36.1750, lng: -115.1372 }, // Las Vegas, NV
                "92064": { lat: 32.9628, lng: -117.0359 }, // Poway, CA
                "92675": { lat: 33.5017, lng: -117.6628 }, // San Juan Capistrano, CA
                "94301": { lat: 37.4419, lng: -122.1430 }, // Palo Alto, CA
                "95014": { lat: 37.3230, lng: -122.0322 }, // Cupertino, CA
                "93001": { lat: 34.2746, lng: -119.2290 }, // Ventura, CA
                "98101": { lat: 47.6062, lng: -122.3321 }, // Seattle, WA
                "98052": { lat: 47.6740, lng: -122.1215 }, // Redmond, WA
                "98004": { lat: 47.6101, lng: -122.2015 }, // Bellevue, WA
                "98039": { lat: 47.6240, lng: -122.2304 }, // Medina, WA
            };
            
            if (zipCoordinates[zipCode]) {
                const { lat, lng } = zipCoordinates[zipCode];
                window.LAT = lat;
                window.LNG = lng;
                window.ZIP_CODE = zipCode;
                mockZipDisplay.textContent = zipCode;
                mockCoordsDisplay.textContent = `${lat}, ${lng}`;
                mockStatusMessage.textContent = `Coordinates found: ${lat}, ${lng}`;
                mockStatusMessage.style.color = "#333";
                return { lat, lng };
            } else {
                mockStatusMessage.textContent = `Error: ZIP code ${zipCode} not found in local database. Using default ZIP ${window.ZIP_CODE}.`;
                mockStatusMessage.style.color = "#d32f2f";
                return null;
            }
        };
        
        async function testZipCode() {
            const zipInput = document.getElementById('testZip');
            const resultsDiv = document.getElementById('zipResults');
            const zipCode = zipInput.value.trim();
            
            resultsDiv.innerHTML = '<p class="info">Testing ZIP code: ' + zipCode + '</p>';
            
            if (!/^\d{5}$/.test(zipCode)) {
                resultsDiv.innerHTML += '<p class="error">❌ Invalid ZIP code format</p>';
                return;
            }
            
            const result = await window.geocodeZipCode(zipCode);
            
            if (result) {
                resultsDiv.innerHTML += `
                    <p class="success">✅ Success!</p>
                    <p>ZIP: ${window.ZIP_CODE}</p>
                    <p>Coordinates: ${window.LAT}, ${window.LNG}</p>
                    <p>Status: ${mockStatusMessage.textContent}</p>
                `;
            } else {
                resultsDiv.innerHTML += `
                    <p class="error">❌ Failed to geocode</p>
                    <p>Status: ${mockStatusMessage.textContent}</p>
                `;
            }
        }
        
        async function testKnownZips() {
            const resultsDiv = document.getElementById('knownZipResults');
            const testZips = ["60010", "60004", "60601", "60614", "60622", "10001", "90210", "89101", "92064", "94301", "98101", "98052"];

            resultsDiv.innerHTML = '<p class="info">Testing known ZIP codes...</p>';
            
            for (const zip of testZips) {
                const result = await window.geocodeZipCode(zip);
                if (result) {
                    resultsDiv.innerHTML += `<p class="success">✅ ${zip}: ${result.lat}, ${result.lng}</p>`;
                } else {
                    resultsDiv.innerHTML += `<p class="error">❌ ${zip}: Failed</p>`;
                }
            }
        }
        
        async function testInvalidZips() {
            const resultsDiv = document.getElementById('invalidZipResults');
            const invalidZips = ["12345", "00000", "99999", "abcde", "123"];
            
            resultsDiv.innerHTML = '<p class="info">Testing invalid ZIP codes...</p>';
            
            for (const zip of invalidZips) {
                if (!/^\d{5}$/.test(zip)) {
                    resultsDiv.innerHTML += `<p class="error">❌ ${zip}: Invalid format (correctly rejected)</p>`;
                    continue;
                }
                
                const result = await window.geocodeZipCode(zip);
                if (!result) {
                    resultsDiv.innerHTML += `<p class="success">✅ ${zip}: Correctly rejected (not in database)</p>`;
                } else {
                    resultsDiv.innerHTML += `<p class="error">❌ ${zip}: Unexpectedly found coordinates</p>`;
                }
            }
        }
        
        // Test on page load
        window.addEventListener('load', function() {
            document.getElementById('results').innerHTML = `
                <div class="test-section">
                    <h3>Initial State</h3>
                    <p>Default ZIP: ${window.ZIP_CODE}</p>
                    <p>Default Coordinates: ${window.LAT}, ${window.LNG}</p>
                </div>
            `;
        });
    </script>
</body>
</html>
